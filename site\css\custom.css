/* 自定义样式 */
body {
	font-family: '<PERSON><PERSON>', 'Microsoft YaHei', sans-serif;
	line-height: 1.6;
}

.custom-navbar {
	background: rgba(0,0,0,0.9);
	padding: 15px 0;
}

.parallax-section {
	background-attachment: fixed;
	background-position: center;
	background-repeat: no-repeat;
	background-size: cover;
	padding: 80px 0;
}

#intro {
	background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('../images/banner.jpg');
	background-size: 100% 100%;
	color: white;
	text-align: center;
	min-height: 100vh;
	display: flex;
	align-items: center;
}

#overview {
	background: #f8f9fa;
	padding: 80px 0;
}

.section-title h2 {
	font-size: 2.5rem;
	margin-bottom: 30px;
	color: #333;
}

.header-container {
	display: flex;
	align-items: center;
}

.logo {
	margin-right: 15px;
}

#title-text {
	color: white !important;
	font-size: 1.2rem;
	text-decoration: none;
}

.navbar-nav .nav-link {
	color: white !important;
	margin: 0 10px;
}

.navbar-nav .nav-link:hover {
	color: #007bff !important;
}

.btn-primary {
	background-color: #007bff;
	border-color: #007bff;
}

.table-bordered {
	border: 1px solid #dee2e6;
}

.table th, .table td {
	padding: 12px;
	vertical-align: middle;
}

footer {
	background: #333;
	color: white;
	padding: 30px 0;
	text-align: center;
}

.go-top {
	position: fixed;
	bottom: 30px;
	right: 30px;
	background: #007bff;
	color: white;
	width: 50px;
	height: 50px;
	text-align: center;
	line-height: 50px;
	border-radius: 50%;
	text-decoration: none;
	display: none;
}

.preloader {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: white;
	z-index: 9999;
	display: flex;
	justify-content: center;
	align-items: center;
}

.sk-rotating-plane {
	width: 40px;
	height: 40px;
	background-color: #007bff;
	animation: sk-rotateplane 1.2s infinite ease-in-out;
}

@keyframes sk-rotateplane {
	0% { transform: perspective(120px) rotateX(0deg) rotateY(0deg); }
	50% { transform: perspective(120px) rotateX(-180.1deg) rotateY(0deg); }
	100% { transform: perspective(120px) rotateX(-180deg) rotateY(-179.9deg); }
}

.organization-section {
	background: white;
	padding: 80px 0;
}

.committee-section {
	background: #f8f9fa;
	padding: 80px 0;
}

.topics-section {
	background: white;
	padding: 80px 0;
}

.register-section {
	background: #003366;
	color: white;
	padding: 80px 0;
}

.contact-section {
	background: #f8f9fa;
	padding: 80px 0;
}

.member-card {
	text-align: center;
	margin-bottom: 30px;
}

.member-card h3 {
	font-size: 1.1rem;
	margin-bottom: 5px;
}

.member-card h4 {
	font-size: 0.9rem;
	color: #666;
	margin-bottom: 5px;
}

.member-card p {
	font-size: 0.8rem;
	color: #888;
}

.topic-card {
	background: white;
	padding: 30px;
	border-radius: 10px;
	box-shadow: 0 5px 15px rgba(0,0,0,0.1);
	margin-bottom: 30px;
	height: 100%;
}

.topic-card h4 {
	color: #007bff;
	margin-bottom: 20px;
}

.organization-item {
	margin-bottom: 15px;
}

.organization-item h3 {
	font-size: 1.1rem;
	margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
	#intro h1 {
		font-size: 2rem !important;
	}
	
	#intro h2 {
		font-size: 1.3rem !important;
	}
	
	#intro h3 {
		font-size: 1rem !important;
	}
	
	.section-title h2 {
		font-size: 2rem;
	}
	
	.topic-card {
		padding: 20px;
	}
	
	.member-card {
		margin-bottom: 20px;
	}
}

/* 平滑滚动 */
html {
	scroll-behavior: smooth;
}

/* 导航栏激活状态 */
.navbar-nav .nav-link.active {
	color: #007bff !important;
	font-weight: 500;
}

/* 卡片悬停效果 */
.topic-card:hover {
	transform: translateY(-5px);
	box-shadow: 0 10px 25px rgba(0,0,0,0.15);
	transition: all 0.3s ease;
}

/* 按钮悬停效果 */
.btn:hover {
	transform: translateY(-2px);
	transition: all 0.3s ease;
}

/* 表格样式优化 */
.table-dark {
	background-color: rgba(255,255,255,0.1);
}

.table-dark th,
.table-dark td {
	border-color: rgba(255,255,255,0.2);
}

/* 文字选择颜色 */
::selection {
	background-color: #007bff;
	color: white;
}

::-moz-selection {
	background-color: #007bff;
	color: white;
}
